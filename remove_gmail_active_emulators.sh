#!/bin/bash

# ============================================================================
# RIMOZIONE GMAIL DA EMULATORI ATTIVI - 2025
# <PERSON><PERSON><PERSON> per rimuovere Gmail solo dagli emulatori avviati
# ============================================================================

echo "🗑️  RIMOZIONE GMAIL DA EMULATORI ATTIVI"
echo "======================================="

ADB_PATH="$HOME/Android/Sdk/platform-tools/adb"

# === FUNZIONI ===

check_adb() {
    if [ ! -f "$ADB_PATH" ]; then
        echo "❌ ADB non trovato in $ADB_PATH"
        exit 1
    fi
    echo "✅ ADB trovato"
}

get_active_devices() {
    echo "🔍 Ricerca emulatori attivi..."
    DEVICES=$($ADB_PATH devices | grep emulator | grep -E "(device|online)" | cut -f1)
    
    if [ -z "$DEVICES" ]; then
        echo "❌ Nessun emulatore attivo trovato"
        echo ""
        echo "💡 ISTRUZIONI:"
        echo "   1. Apri Android Studio"
        echo "   2. Vai in AVD Manager"
        echo "   3. Avvia gli emulatori che vuoi"
        echo "   4. Riavvia questo script"
        exit 1
    fi
    
    DEVICE_COUNT=$(echo "$DEVICES" | wc -l)
    echo "✅ Trovati $DEVICE_COUNT emulatori attivi:"
    echo "$DEVICES" | sed 's/^/   • /'
    echo ""
}

remove_gmail_from_device() {
    local device_id=$1
    local index=$2
    local total=$3
    
    echo "🎮 [$index/$total] Rimozione Gmail da: $device_id"
    
    # Verifica connessione
    if ! $ADB_PATH -s "$device_id" shell echo "test" >/dev/null 2>&1; then
        echo "   ❌ Dispositivo non raggiungibile"
        return 1
    fi
    
    # Verifica se Gmail è presente
    if ! $ADB_PATH -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
        echo "   ✅ Gmail già assente"
        return 0
    fi
    
    echo "   🔧 Rimozione Gmail in corso..."
    
    # Metodo 1: Disable
    if $ADB_PATH -s "$device_id" shell pm disable-user --user 0 com.google.android.gm >/dev/null 2>&1; then
        echo "   ✅ Gmail disabilitato"
    else
        echo "   ⚠️  Disable fallito"
    fi
    
    # Metodo 2: Uninstall
    if $ADB_PATH -s "$device_id" shell pm uninstall --user 0 com.google.android.gm >/dev/null 2>&1; then
        echo "   ✅ Gmail disinstallato"
    else
        echo "   ⚠️  Uninstall fallito"
    fi
    
    # Metodo 3: Hide
    if $ADB_PATH -s "$device_id" shell pm hide --user 0 com.google.android.gm >/dev/null 2>&1; then
        echo "   ✅ Gmail nascosto"
    else
        echo "   ⚠️  Hide fallito"
    fi
    
    # Metodo 4: Clear data
    if $ADB_PATH -s "$device_id" shell pm clear com.google.android.gm >/dev/null 2>&1; then
        echo "   ✅ Dati Gmail cancellati"
    else
        echo "   ⚠️  Clear fallito"
    fi
    
    # Verifica finale
    sleep 2
    if $ADB_PATH -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
        echo "   ⚠️  Gmail ancora presente (ma probabilmente disabilitato)"
        return 1
    else
        echo "   🎉 Gmail completamente rimosso!"
        return 0
    fi
}

verify_playstore() {
    local device_id=$1
    
    echo "   🏪 Verifica Play Store..."
    if $ADB_PATH -s "$device_id" shell pm list packages | grep -q "com.android.vending"; then
        echo "   ✅ Play Store presente e funzionante"
        return 0
    else
        echo "   ❌ Play Store non trovato!"
        return 1
    fi
}

# === ESECUZIONE PRINCIPALE ===

echo "📱 RIMOZIONE GMAIL DA EMULATORI ATTIVI"
echo "   • Mantiene Play Store attivo"
echo "   • Rimuove solo Gmail (com.google.android.gm)"
echo "   • Processa solo emulatori avviati"
echo ""

# Verifica prerequisiti
check_adb

# Trova emulatori attivi
get_active_devices

# Conferma utente
echo "❓ Procedere con la rimozione Gmail da $DEVICE_COUNT emulatori?"
echo "   (Premi ENTER per continuare, CTRL+C per annullare)"
read -r

echo ""
echo "🚀 INIZIO RIMOZIONE GMAIL"
echo "========================"

success_count=0
failed_count=0
index=1

for device_id in $DEVICES; do
    echo ""
    
    if remove_gmail_from_device "$device_id" "$index" "$DEVICE_COUNT"; then
        verify_playstore "$device_id"
        ((success_count++))
    else
        ((failed_count++))
    fi
    
    ((index++))
done

echo ""
echo "📊 RISULTATI FINALI:"
echo "==================="
echo "✅ Successi: $success_count/$DEVICE_COUNT"
echo "❌ Fallimenti: $failed_count"

if [ $success_count -eq $DEVICE_COUNT ]; then
    echo ""
    echo "🎉 GMAIL RIMOSSO DA TUTTI GLI EMULATORI!"
    echo ""
    echo "✅ VERIFICHE COMPLETATE:"
    echo "   • Gmail disabilitato/rimosso"
    echo "   • Play Store mantenuto attivo"
    echo "   • Emulatori pronti per il gaming"
    echo ""
    echo "🎮 Gli emulatori sono ora ottimizzati per il gaming!"
else
    echo ""
    echo "⚠️  Alcuni emulatori potrebbero ancora avere Gmail"
    echo "   • Controlla manualmente gli emulatori falliti"
    echo "   • Riavvia il script se necessario"
fi

echo ""
echo "💡 NOTA: Riavvia gli emulatori per applicare completamente le modifiche"
