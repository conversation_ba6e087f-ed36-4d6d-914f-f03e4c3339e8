#!/bin/bash

# Script finale per rimozione Gmail da tutti i 47 emulatori
# Approccio batch: 5 emulatori per volta

echo "📧 RIMOZIONE GMAIL DA TUTTI I 47 EMULATORI"
echo "=========================================="
echo "Approccio: 5 emulatori per batch per ottimizzare performance"
echo ""

# Lista completa dei 47 giochi
games=(
    "ASTRA_Knights_of_Veda" "Ace_Racer" "Aether_Gazer" "Arknights" "Ash_Echoes"
    "Black_Beacon" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy_Isekai_Adventure"
    "CookieRun_Kingdom" "CookieRun_OvenBreak" "CookieRun_Tower_of_Adventures"
    "CounterSide" "DanMachi_BATTLE_CHRONICLE" "Dislyte" "Echocalypse_Scarlet_Covenant"
    "Epic_Seven" "Etheria_Restart" "Eversoul" "Farlight_84" "Figure_Fantasy"
    "Genshin_Impact" "Girls_Frontline_2_Exilium" "Go_Go_Muffin" "Heaven_Burns_Red"
    "Higan_Eruthyll" "Honkai_Star_Rail" "Infinity_Nikki" "Jujutsu_Kaisen_Phantom_Parade"
    "MementoMori_AFKRPG" "Metal_Slug_Awakening" "Neural_Cloud" "NIKKE_Goddess_of_Victory"
    "Ni_no_Kuni_Cross_Worlds" "OUTERPLANE_Strategy_Anime" "Path_to_Nowhere"
    "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999"
    "Snowbreak_Containment_Zone" "Solo_Leveling_Arise" "STARSEED_Asnia_Trigger"
    "Tower_of_God_Great_Journey" "Tower_of_God_NEW_WORLD" "Uma_Musume_Pretty_Derby"
    "Wuthering_Waves" "Zenless_Zone_Zero"
)

BATCH_SIZE=5
total_games=${#games[@]}
total_batches=$(( (total_games + BATCH_SIZE - 1) / BATCH_SIZE ))
processed_count=0
success_count=0
failed_count=0

echo "📊 Statistiche:"
echo "   Emulatori totali: $total_games"
echo "   Batch size: $BATCH_SIZE"
echo "   Batch totali: $total_batches"
echo ""

# Funzione per rimuovere Gmail da un emulatore
remove_gmail_from_emulator() {
    local device_id=$1
    local emulator_name=$2
    
    echo "      🔧 Processando: $emulator_name ($device_id)"
    
    # Verifica boot completato
    local boot_completed=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell getprop sys.boot_completed 2>/dev/null | tr -d '\r')
    
    if [ "$boot_completed" = "1" ]; then
        # Rimozione Gmail aggressiva
        ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm disable-user --user 0 com.google.android.gm >/dev/null 2>&1
        ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm uninstall --user 0 com.google.android.gm >/dev/null 2>&1
        ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm hide --user 0 com.google.android.gm >/dev/null 2>&1
        ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm clear com.google.android.gm >/dev/null 2>&1
        
        # Verifica rimozione
        local gmail_after=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep "com.google.android.gm" | wc -l)
        
        if [ "$gmail_after" -eq 0 ]; then
            echo "         ✅ Gmail rimosso completamente"
            return 0
        else
            echo "         ⚠️  Gmail nascosto/disabilitato"
            return 0
        fi
    else
        echo "         ❌ Emulatore non pronto"
        return 1
    fi
}

# Funzione per processare un batch
process_batch() {
    local batch_num=$1
    local start_idx=$2
    local end_idx=$3
    
    echo "📦 BATCH $batch_num/$total_batches"
    echo "   Range: $((start_idx + 1))-$((end_idx + 1))"
    
    # Avvia emulatori del batch
    local batch_games=()
    local batch_pids=()
    
    echo "   🚀 Avvio emulatori..."
    for i in $(seq $start_idx $end_idx); do
        if [ $i -lt $total_games ]; then
            game_name="${games[$i]}"
            batch_games+=("$game_name")
            
            echo "      Avviando: $game_name"
            ~/Android/Sdk/emulator/emulator -avd "$game_name" -no-window -no-audio -no-snapshot-save &
            batch_pids+=($!)
            sleep 2
        fi
    done
    
    # Attesa avvio
    echo "   ⏳ Attesa avvio (60 secondi)..."
    sleep 60
    
    # Ottieni device IDs
    local active_devices=$(~/Android/Sdk/platform-tools/adb devices | grep "emulator" | grep "device" | cut -f1)
    
    if [ -n "$active_devices" ]; then
        echo "   📱 Emulatori attivi: $(echo "$active_devices" | wc -l)"
        
        # Rimuovi Gmail da tutti i device attivi
        echo "   🔧 Rimozione Gmail..."
        local batch_success=0
        local batch_failed=0
        
        for device in $active_devices; do
            # Ottieni nome emulatore
            local avd_name=$(~/Android/Sdk/platform-tools/adb -s "$device" emu avd name 2>/dev/null | tr -d '\r')
            
            if remove_gmail_from_emulator "$device" "$avd_name"; then
                ((batch_success++))
                ((success_count++))
            else
                ((batch_failed++))
                ((failed_count++))
            fi
            ((processed_count++))
        done
        
        echo "   📊 Batch risultati: ✅$batch_success ❌$batch_failed"
        
        # Chiudi emulatori
        echo "   🔄 Chiusura emulatori..."
        for device in $active_devices; do
            ~/Android/Sdk/platform-tools/adb -s "$device" emu kill >/dev/null 2>&1
        done
        
        # Termina processi
        for pid in "${batch_pids[@]}"; do
            kill $pid >/dev/null 2>&1
        done
        
        sleep 5
    else
        echo "   ❌ Nessun emulatore attivo trovato"
        ((failed_count += ${#batch_games[@]}))
    fi
    
    echo ""
}

# Processamento di tutti i batch
echo "🚀 INIZIO PROCESSAMENTO BATCH"
echo ""

for batch in $(seq 1 $total_batches); do
    start_idx=$(( (batch - 1) * BATCH_SIZE ))
    end_idx=$(( start_idx + BATCH_SIZE - 1 ))
    
    if [ $end_idx -ge $total_games ]; then
        end_idx=$((total_games - 1))
    fi
    
    process_batch $batch $start_idx $end_idx
    
    # Pausa tra batch
    if [ $batch -lt $total_batches ]; then
        echo "⏸️  Pausa 10 secondi prima del prossimo batch..."
        sleep 10
    fi
done

echo "🎉 PROCESSAMENTO COMPLETATO!"
echo ""
echo "📊 RISULTATI FINALI:"
echo "✅ Successi: $success_count"
echo "❌ Fallimenti: $failed_count"
echo "📱 Totale processati: $processed_count"
echo "🎯 Percentuale successo: $(( success_count * 100 / total_games ))%"

if [ $success_count -eq $total_games ]; then
    echo ""
    echo "🎉 TUTTI I 47 EMULATORI PULITI!"
    echo "   ✅ Play Store: PRESENTE"
    echo "   ❌ Gmail: RIMOSSO"
    echo "   🎮 Pronti per il gaming!"
else
    echo ""
    echo "⚠️  Alcuni emulatori potrebbero necessitare riprocessamento"
fi
