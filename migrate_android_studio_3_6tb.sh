#!/bin/bash

# ============================================================================
# MIGRAZIONE ANDROID STUDIO SU PARTIZIONE 3.6TB
# Sposta Android Studio dalla partizione 49GB (78% piena) alla 3.6TB (3% piena)
# Risolve definitivamente il problema di blocco con emulatori multipli
# ============================================================================

echo "🚚 MIGRAZIONE ANDROID STUDIO SU PARTIZIONE 3.6TB"
echo "================================================="

# === CONFIGURAZIONI ===
SOURCE_ANDROID_STUDIO="/opt/android-studio"
TARGET_ANDROID_STUDIO="/home/<USER>/android-studio"
TARGET_SDK="/home/<USER>/Android/Sdk"
TARGET_AVD="/home/<USER>/.android/avd"

# === FUNZIONI ===

check_current_situation() {
    echo "🔍 VERIFICA SITUAZIONE ATTUALE"
    echo "=============================="
    
    echo "💿 Partizioni:"
    df -h | grep -E "/dev/sdc[23]"
    
    echo ""
    echo "📍 Posizioni attuali:"
    
    # Android Studio
    if [ -d "$SOURCE_ANDROID_STUDIO" ]; then
        STUDIO_SIZE=$(du -sh "$SOURCE_ANDROID_STUDIO" 2>/dev/null | cut -f1)
        echo "   Android Studio: $SOURCE_ANDROID_STUDIO ($STUDIO_SIZE) - PARTIZIONE 49GB ❌"
    else
        echo "   Android Studio: NON TROVATO"
    fi
    
    # SDK
    if [ -d "~/Android/Sdk" ]; then
        SDK_SIZE=$(du -sh ~/Android/Sdk 2>/dev/null | cut -f1)
        echo "   Android SDK: ~/Android/Sdk ($SDK_SIZE) - PARTIZIONE 3.6TB ✅"
    else
        echo "   Android SDK: NON TROVATO"
    fi
    
    # AVD
    if [ -d ~/.config/.android/avd ]; then
        AVD_SIZE=$(du -sh ~/.config/.android/avd 2>/dev/null | cut -f1)
        echo "   Emulatori AVD: ~/.config/.android/avd ($AVD_SIZE) - PARTIZIONE 49GB ❌"
    else
        echo "   Emulatori AVD: NON TROVATI"
    fi
    
    echo ""
}

stop_android_processes() {
    echo "🛑 TERMINAZIONE PROCESSI ANDROID"
    echo "==============================="
    
    echo "🔪 Terminazione Android Studio..."
    pkill -f "android-studio" 2>/dev/null
    pkill -f "studio" 2>/dev/null
    
    echo "🔪 Terminazione emulatori..."
    pkill -f "qemu-system" 2>/dev/null
    pkill -f "emulator" 2>/dev/null
    
    echo "⏱️  Attesa terminazione processi..."
    sleep 5
    
    echo "✅ Processi terminati"
    echo ""
}

migrate_android_studio() {
    echo "🚚 MIGRAZIONE ANDROID STUDIO"
    echo "============================"
    
    if [ ! -d "$SOURCE_ANDROID_STUDIO" ]; then
        echo "❌ Android Studio non trovato in $SOURCE_ANDROID_STUDIO"
        return 1
    fi
    
    echo "📦 Copia Android Studio su partizione 3.6TB..."
    echo "   Da: $SOURCE_ANDROID_STUDIO"
    echo "   A:  $TARGET_ANDROID_STUDIO"
    
    # Crea directory target
    mkdir -p "$(dirname "$TARGET_ANDROID_STUDIO")"
    
    # Copia con progress
    sudo rsync -av --progress "$SOURCE_ANDROID_STUDIO/" "$TARGET_ANDROID_STUDIO/"
    
    if [ $? -eq 0 ]; then
        echo "✅ Android Studio copiato con successo"
        
        # Cambia ownership
        sudo chown -R sebyx:sebyx "$TARGET_ANDROID_STUDIO"
        
        echo "✅ Ownership corretta impostata"
    else
        echo "❌ Errore durante la copia"
        return 1
    fi
    
    echo ""
}

migrate_avd_emulators() {
    echo "📱 MIGRAZIONE EMULATORI AVD"
    echo "=========================="
    
    if [ ! -d ~/.config/.android/avd ]; then
        echo "ℹ️  Nessun emulatore da migrare"
        return 0
    fi
    
    echo "📦 Spostamento emulatori su partizione 3.6TB..."
    echo "   Da: ~/.config/.android/avd"
    echo "   A:  $TARGET_AVD"
    
    # Crea directory target
    mkdir -p "$(dirname "$TARGET_AVD")"
    
    # Sposta emulatori
    mv ~/.config/.android/avd "$TARGET_AVD"
    
    # Crea symlink per compatibilità
    ln -sf "$TARGET_AVD" ~/.config/.android/avd
    
    echo "✅ Emulatori migrati con symlink di compatibilità"
    echo ""
}

update_desktop_entries() {
    echo "🖥️  AGGIORNAMENTO DESKTOP ENTRIES"
    echo "==============================="
    
    # Desktop entry principale
    cat > ~/.local/share/applications/android-studio.desktop << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Android Studio
Icon=$TARGET_ANDROID_STUDIO/bin/studio.png
Exec=$TARGET_ANDROID_STUDIO/bin/studio
Comment=The Drive to Develop
Categories=Development;IDE;
Terminal=false
StartupWMClass=jetbrains-studio
StartupNotify=true
EOF
    
    echo "✅ Desktop entry aggiornato per nuova posizione"
    echo ""
}

create_launcher_script() {
    echo "🚀 CREAZIONE LAUNCHER OTTIMIZZATO"
    echo "================================"
    
    cat > ~/launch_android_studio_3_6tb.sh << EOF
#!/bin/bash

# ============================================================================
# LAUNCHER ANDROID STUDIO SU PARTIZIONE 3.6TB
# Avvia Android Studio dalla posizione ottimale con spazio illimitato
# ============================================================================

echo "🚀 Avvio Android Studio da partizione 3.6TB"

# Pulizia preventiva
pkill -f "android-studio" 2>/dev/null
sleep 2

# Variabili ambiente ottimizzate
export STUDIO_JVM_OPTS="-Xms4g -Xmx12g -XX:ReservedCodeCacheSize=2g"
export ANDROID_SDK_ROOT="$HOME/Android/Sdk"
export ANDROID_AVD_HOME="$HOME/.android/avd"

# Verifica spazio disponibile
AVAILABLE_SPACE=\$(df -BG /home | awk 'NR==2 {print \$4}' | sed 's/G//')
echo "💿 Spazio disponibile: \${AVAILABLE_SPACE}GB"

if [ \$AVAILABLE_SPACE -lt 50 ]; then
    echo "⚠️  ATTENZIONE: Spazio limitato (\${AVAILABLE_SPACE}GB)"
else
    echo "✅ Spazio sufficiente per emulatori multipli"
fi

# Avvio Android Studio
echo "🎯 Avvio da: $TARGET_ANDROID_STUDIO/bin/studio"
"$TARGET_ANDROID_STUDIO/bin/studio" &

echo "✅ Android Studio avviato da partizione 3.6TB"
EOF
    
    chmod +x ~/launch_android_studio_3_6tb.sh
    
    echo "✅ Launcher creato: ~/launch_android_studio_3_6tb.sh"
    echo ""
}

update_environment_variables() {
    echo "🔧 AGGIORNAMENTO VARIABILI AMBIENTE"
    echo "=================================="
    
    # Backup .bashrc
    cp ~/.bashrc ~/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
    
    # Rimuovi vecchie configurazioni Android
    sed -i '/# Android Studio/,/# End Android Studio/d' ~/.bashrc
    sed -i '/ANDROID_SDK_ROOT/d' ~/.bashrc
    sed -i '/ANDROID_AVD_HOME/d' ~/.bashrc
    
    # Aggiungi nuove configurazioni
    cat >> ~/.bashrc << EOF

# Android Studio Configuration - 3.6TB Partition
export ANDROID_SDK_ROOT="\$HOME/Android/Sdk"
export ANDROID_AVD_HOME="\$HOME/.android/avd"
export PATH="\$PATH:\$ANDROID_SDK_ROOT/platform-tools:\$ANDROID_SDK_ROOT/tools"

# Android Studio Launcher Alias
alias android-studio="$TARGET_ANDROID_STUDIO/bin/studio"
alias studio="$TARGET_ANDROID_STUDIO/bin/studio"
EOF
    
    echo "✅ Variabili ambiente aggiornate in ~/.bashrc"
    echo ""
}

create_monitoring_script() {
    echo "📊 CREAZIONE SCRIPT MONITORAGGIO 3.6TB"
    echo "======================================"
    
    cat > ~/monitor_android_3_6tb.sh << 'EOF'
#!/bin/bash

echo "📊 MONITORAGGIO ANDROID STUDIO SU PARTIZIONE 3.6TB"
echo "=================================================="
echo "Data: $(date)"
echo ""

# Spazio partizione 3.6TB
echo "💿 PARTIZIONE 3.6TB (/home):"
df -h /home
HOME_USAGE=$(df /home | awk 'NR==2 {print $5}' | sed 's/%//')
HOME_AVAILABLE=$(df -BG /home | awk 'NR==2 {print $4}' | sed 's/G//')
echo "Utilizzo: ${HOME_USAGE}% | Disponibile: ${HOME_AVAILABLE}GB"
echo "Status: $([ $HOME_USAGE -lt 50 ] && echo '✅ OTTIMALE' || echo '⚠️ MONITORARE')"
echo ""

# Spazio partizione root (49GB)
echo "💿 PARTIZIONE ROOT (/):"
df -h /
ROOT_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
echo "Utilizzo: ${ROOT_USAGE}% | Status: $([ $ROOT_USAGE -lt 80 ] && echo '✅ OK' || echo '🚨 CRITICO')"
echo ""

# Android Studio
echo "🎯 ANDROID STUDIO:"
if pgrep -f "android-studio" >/dev/null; then
    STUDIO_PID=$(pgrep -f "android-studio")
    STUDIO_RAM=$(ps -p $STUDIO_PID -o rss= | awk '{print $1/1024 "MB"}')
    echo "Status: ✅ ATTIVO (PID: $STUDIO_PID, RAM: $STUDIO_RAM)"
else
    echo "Status: ⭕ NON ATTIVO"
fi
echo ""

# Emulatori
echo "📱 EMULATORI:"
EMU_COUNT=$(pgrep -f "qemu-system" | wc -l)
echo "Emulatori attivi: $EMU_COUNT"

if [ $EMU_COUNT -gt 0 ]; then
    echo "Dettagli emulatori:"
    ps aux | grep qemu-system | grep -v grep | awk '{print "  PID: " $2 " | RAM: " $6/1024 "MB | CPU: " $3 "%"}'
    
    # Calcola spazio utilizzato dagli emulatori
    if [ -d "$HOME/.android/avd" ]; then
        AVD_SIZE=$(du -sh "$HOME/.android/avd" 2>/dev/null | cut -f1)
        echo "Spazio emulatori: $AVD_SIZE"
    fi
fi
echo ""

# RAM sistema
echo "💾 MEMORIA SISTEMA:"
free -h | head -2
RAM_USAGE=$(free | awk '/^Mem:/ {printf "%.1f", $3/$2 * 100}')
echo "Utilizzo RAM: ${RAM_USAGE}%"
echo ""

# Raccomandazioni
echo "💡 RACCOMANDAZIONI:"
if [ $HOME_AVAILABLE -gt 1000 ]; then
    echo "✅ Spazio eccellente per emulatori multipli (${HOME_AVAILABLE}GB disponibili)"
    echo "   • Puoi usare 10+ emulatori simultanei senza problemi"
    echo "   • Configura emulatori con 6GB RAM ciascuno"
else
    echo "⚠️  Spazio limitato (${HOME_AVAILABLE}GB disponibili)"
    echo "   • Usa emulatori con 3-4GB RAM ciascuno"
fi

if [ ${RAM_USAGE%.*} -gt 80 ]; then
    echo "⚠️  RAM alta (${RAM_USAGE}%) - Considera di chiudere applicazioni"
else
    echo "✅ RAM ottimale (${RAM_USAGE}%)"
fi

echo ""
echo "🎯 COMANDI UTILI:"
echo "   Avvia Android Studio: bash ~/launch_android_studio_3_6tb.sh"
echo "   Monitoraggio: bash ~/monitor_android_3_6tb.sh"
EOF
    
    chmod +x ~/monitor_android_3_6tb.sh
    
    echo "✅ Monitoraggio creato: ~/monitor_android_3_6tb.sh"
    echo ""
}

verify_migration() {
    echo "🔍 VERIFICA MIGRAZIONE"
    echo "====================="
    
    echo "📍 Nuove posizioni:"
    
    # Android Studio
    if [ -d "$TARGET_ANDROID_STUDIO" ]; then
        STUDIO_SIZE=$(du -sh "$TARGET_ANDROID_STUDIO" 2>/dev/null | cut -f1)
        echo "   ✅ Android Studio: $TARGET_ANDROID_STUDIO ($STUDIO_SIZE)"
    else
        echo "   ❌ Android Studio: NON MIGRATO"
    fi
    
    # AVD
    if [ -d "$TARGET_AVD" ]; then
        AVD_SIZE=$(du -sh "$TARGET_AVD" 2>/dev/null | cut -f1)
        echo "   ✅ Emulatori AVD: $TARGET_AVD ($AVD_SIZE)"
    else
        echo "   ❌ Emulatori AVD: NON MIGRATI"
    fi
    
    # Symlink
    if [ -L ~/.config/.android/avd ]; then
        echo "   ✅ Symlink compatibilità: ~/.config/.android/avd → $TARGET_AVD"
    else
        echo "   ❌ Symlink compatibilità: MANCANTE"
    fi
    
    echo ""
    echo "💿 Spazio partizioni dopo migrazione:"
    df -h | grep -E "/dev/sdc[23]"
    
    echo ""
}

# === ESECUZIONE PRINCIPALE ===

echo "🚚 MIGRAZIONE COMPLETA ANDROID STUDIO SU PARTIZIONE 3.6TB"
echo "   • Risolve definitivamente il problema di spazio"
echo "   • Permette emulatori multipli senza blocchi"
echo "   • Sposta tutto dalla partizione 49GB (78%) alla 3.6TB (3%)"
echo ""

# Verifica situazione attuale
check_current_situation

echo "❓ Procedere con la migrazione? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "🚀 INIZIO MIGRAZIONE..."
    echo "====================="
    
    # Step 1: Termina processi
    stop_android_processes
    
    # Step 2: Migra Android Studio
    migrate_android_studio
    
    # Step 3: Migra emulatori
    migrate_avd_emulators
    
    # Step 4: Aggiorna desktop entries
    update_desktop_entries
    
    # Step 5: Crea launcher
    create_launcher_script
    
    # Step 6: Aggiorna variabili ambiente
    update_environment_variables
    
    # Step 7: Crea monitoraggio
    create_monitoring_script
    
    # Step 8: Verifica migrazione
    verify_migration
    
    echo ""
    echo "🎉 MIGRAZIONE COMPLETATA!"
    echo "========================"
    echo ""
    echo "✅ RISULTATI:"
    echo "   🚚 Android Studio migrato su partizione 3.6TB"
    echo "   📱 Emulatori migrati su partizione 3.6TB"
    echo "   🔗 Symlink di compatibilità creati"
    echo "   🚀 Launcher ottimizzato creato"
    echo "   📊 Script monitoraggio creato"
    echo ""
    echo "🎯 PROSSIMI PASSI:"
    echo "   1. Riavvia il terminale: source ~/.bashrc"
    echo "   2. Avvia Android Studio: bash ~/launch_android_studio_3_6tb.sh"
    echo "   3. Verifica: bash ~/monitor_android_3_6tb.sh"
    echo ""
    echo "💡 VANTAGGI:"
    echo "   • Spazio illimitato per emulatori (3.3TB disponibili)"
    echo "   • Nessun blocco del sistema"
    echo "   • Prestazioni ottimali"
    echo "   • Supporto per 10+ emulatori simultanei"
    echo ""
    echo "⚠️  NOTA: Puoi rimuovere la vecchia installazione da /opt/android-studio"
    echo "   quando sei sicuro che tutto funzioni correttamente."
    echo ""
    echo "✅ SISTEMA PRONTO PER EMULATORI MULTIPLI SENZA LIMITI!"
    
else
    echo ""
    echo "❌ MIGRAZIONE ANNULLATA"
    echo "Il sistema mantiene la configurazione attuale."
fi
