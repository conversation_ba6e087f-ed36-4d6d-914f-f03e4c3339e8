#!/bin/bash

# Script per correggere le configurazioni degli emulatori

echo "🔧 CORREZIONE CONFIGURAZIONI EMULATORI"
echo "======================================"

# Lista tutti gli emulatori
emulators=($(~/Android/Sdk/emulator/emulator -list-avds))
total=${#emulators[@]}

echo "📊 Emulatori da correggere: $total"
echo ""

fixed_count=0

for emulator_name in "${emulators[@]}"; do
    if [ -n "$emulator_name" ]; then
        config_file="$HOME/.config/.android/avd/${emulator_name}.avd/config.ini"
        
        if [ -f "$config_file" ]; then
            echo "🔧 Correggendo: $emulator_name"
            
            # Backup configurazione originale
            cp "$config_file" "${config_file}.backup"
            
            # Crea configurazione corretta e funzionante
            cat > "$config_file" << EOF
# === CONFIGURAZIONE CORRETTA EMULATORE ===
avd.ini.displayname=$emulator_name
avd.ini.encoding=UTF-8
AvdId=$emulator_name

# === SYSTEM IMAGE CORRETTA ===
image.sysdir.1=system-images/android-34/google_apis_playstore/x86_64/
tag.display=Google Play
tag.id=google_apis_playstore
tag.ids=google_apis_playstore

# === ARCHITETTURA ===
abi.type=x86_64

# === PLAY STORE ABILITATO ===
PlayStore.enabled=yes

# === HARDWARE CONFIGURATION ===
hw.ramSize=4096
vm.heapSize=256
hw.cpu.ncore=2
hw.gpu.enabled=yes
hw.gpu.mode=auto

# === DISPLAY ===
hw.lcd.width=1080
hw.lcd.height=1920
hw.lcd.density=420

# === AUDIO/INPUT ===
hw.audioInput=yes
hw.audioOutput=yes
hw.keyboard=yes
hw.dPad=no
hw.trackBall=no
hw.mainKeys=yes

# === SENSORS ===
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.orientation=yes
hw.sensors.proximity=yes

# === CAMERA ===
hw.camera.back=emulated
hw.camera.front=emulated

# === STORAGE ===
disk.dataPartition.size=6442450944
hw.sdCard=yes
sdcard.size=512M

# === NETWORK ===
hw.wifi=yes
hw.gps=yes

# === DEVICE INFO ===
hw.device.manufacturer=Google
hw.device.name=pixel_4

# === PERFORMANCE ===
hw.arc=false
hw.initialOrientation=Portrait
showDeviceFrame=no
skin.dynamic=yes

# === BOOT ===
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=no
fastboot.forceFastBoot=yes
EOF
            
            echo "   ✅ Configurazione corretta applicata"
            ((fixed_count++))
        else
            echo "   ❌ File config non trovato: $emulator_name"
        fi
    fi
done

echo ""
echo "📊 RISULTATI:"
echo "✅ Emulatori corretti: $fixed_count/$total"

if [ $fixed_count -gt 0 ]; then
    echo ""
    echo "🎉 CONFIGURAZIONI CORRETTE!"
    echo ""
    echo "📋 CARATTERISTICHE APPLICATE:"
    echo "   🤖 Android: 14 (API 34)"
    echo "   🏪 Play Store: ABILITATO"
    echo "   🧠 RAM: 4GB (bilanciata)"
    echo "   🖥️  CPU: 2 core (stabile)"
    echo "   📱 Risoluzione: 1080x1920 (standard)"
    echo "   🎮 GPU: Auto (compatibile)"
    echo ""
    echo "✅ GLI EMULATORI ORA DOVREBBERO FUNZIONARE!"
    echo ""
    echo "🧪 TEST RACCOMANDATO:"
    echo "   Prova ad aprire un emulatore da Android Studio"
fi
