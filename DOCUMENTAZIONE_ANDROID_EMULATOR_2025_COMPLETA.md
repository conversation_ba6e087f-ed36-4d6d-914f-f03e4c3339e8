# DOCUMENTAZIONE COMPLETA ANDROID EMULATOR 2025

## 📋 **INDICE**
1. [Panoramica Generale](#panoramica-generale)
2. [Requisiti di Sistema](#requisiti-di-sistema)
3. [Accelerazione Hardware](#accelerazione-hardware)
4. [System Images Disponibili](#system-images-disponibili)
5. [Creazione AVD Ottimale](#creazione-avd-ottimale)
6. [Configurazioni Hardware](#configurazioni-hardware)
7. [Ottimizzazioni Gaming](#ottimizzazioni-gaming)
8. [Troubleshooting](#troubleshooting)

---

## 🔍 **PANORAMICA GENERALE**

### Android Studio 2025 (Narwhal)
- **Versione**: 2025.1.1.14-1
- **Emulator**: 35.6.11.0 (build_id 13610412)
- **Supporto**: Android 15 (API 35) + retrocompatibilità
- **Architetture**: x86_64, arm64-v8a

### Novità 2025
- **Migliorata accelerazione GPU** con SwiftShader Indirect
- **Supporto 16KB page sizes** (obbligatorio da novembre 2025)
- **Hypervisor Framework** ottimizzato per Apple Silicon
- **WHPX migliorato** per Windows 11
- **KVM ottimizzato** per Linux

---

## 💻 **REQUISITI DI SISTEMA**

### Requisiti Minimi
```
CPU: Intel i5-8xxx / AMD Ryzen 5 3xxx / Apple M1
RAM: 16GB (8GB Studio + 8GB Emulator)
Storage: 8GB liberi per SDK
GPU: Supporto OpenGL ES 3.0+
```

### Requisiti Raccomandati Gaming
```
CPU: Intel i7-12xxx / AMD Ryzen 7 5xxx / Apple M2 Pro
RAM: 32GB (16GB Studio + 16GB Emulatori)
Storage: 50GB+ SSD NVMe
GPU: Dedicata con 4GB+ VRAM
```

### Supporto Architetture
- **x86_64**: Intel/AMD (raccomandato per gaming)
- **arm64-v8a**: Apple Silicon, ARM nativi
- **x86**: Legacy (non raccomandato)

---

## ⚡ **ACCELERAZIONE HARDWARE**

### Windows (WHPX vs AEHD)
```bash
# Verifica supporto
emulator -accel-check

# WHPX (Raccomandato)
- Windows 10/11 + Hyper-V
- Migliore compatibilità
- Prestazioni bilanciate

# AEHD (Alternative)
- Hyper-V disabilitato
- Prestazioni superiori
- Meno compatibile
```

### macOS (Hypervisor.Framework)
```bash
# Automatico da macOS 10.10+
# Apple Silicon: accelerazione nativa
# Intel Mac: VT-x richiesto
```

### Linux (KVM)
```bash
# Installazione Ubuntu/Debian
sudo apt-get install qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils

# Verifica
sudo kvm-ok
lscpu | grep Virtualization
```

---

## 💿 **SYSTEM IMAGES DISPONIBILI**

### Tipi di System Image
1. **AOSP (default)**
   - ❌ Gmail, ❌ Play Store, ❌ Google Play Services
   - ✅ Puro Android, massime prestazioni
   - 🎯 Uso: Testing, sviluppo base

2. **Google APIs**
   - ❌ Gmail, ❌ Play Store, ✅ Google Play Services
   - ✅ Maps, Location, Firebase
   - 🎯 Uso: App con servizi Google

3. **Google Play Store**
   - ✅ Gmail, ✅ Play Store, ✅ Google Play Services
   - ✅ Completo ecosistema Google
   - 🎯 Uso: Gaming, app consumer

### Versioni Android Raccomandate 2025
```
Android 15 (API 35): Ultima versione, 16KB pages
Android 14 (API 34): Stabile, gaming ottimale
Android 13 (API 33): Compatibilità estesa
Android 12 (API 31): Legacy support
```

---

## 🛠️ **CREAZIONE AVD OTTIMALE**

### Comando Base Corretto
```bash
# Template gaming ottimizzato
~/Android/Sdk/cmdline-tools/latest/bin/avdmanager create avd \
    -n "NOME_GIOCO" \
    -k "system-images;android-34;google_apis_playstore;x86_64" \
    -d "pixel_4" \
    --abi "x86_64" \
    -f
```

### Parametri Critici
- **-k**: System image path (DEVE essere x86_64)
- **--abi**: Architettura esplicita (evita ARM)
- **-d**: Device profile (pixel_4 raccomandato)
- **-f**: Force overwrite

### Device Profiles Raccomandati
```
pixel_4: 1080x2280, 440dpi (bilanciato)
pixel_6_pro: 1440x3120, 512dpi (high-end)
Nexus_5X: 1080x1920, 420dpi (compatibilità)
```

---

## ⚙️ **CONFIGURAZIONI HARDWARE**

### Config.ini Ottimizzato Gaming
```ini
# === CORE SYSTEM ===
abi.type=x86_64
image.sysdir.1=system-images/android-34/google_apis_playstore/x86_64/
tag.id=google_apis_playstore
PlayStore.enabled=yes

# === PERFORMANCE ===
hw.ramSize=6144          # 6GB RAM (gaming)
vm.heapSize=512          # 512MB heap
hw.cpu.ncore=4           # 4 CPU cores
hw.cpu.arch=x86_64       # Architettura

# === GRAPHICS ===
hw.gpu.enabled=yes
hw.gpu.mode=auto         # auto/host/swiftshader_indirect
hw.lcd.width=1080
hw.lcd.height=1920
hw.lcd.density=420

# === STORAGE ===
disk.dataPartition.size=6442450944  # 6GB
hw.sdCard=yes
sdcard.size=1024M

# === SENSORS ===
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.orientation=yes
hw.gps=yes

# === AUDIO/INPUT ===
hw.audioInput=yes
hw.audioOutput=yes
hw.keyboard=yes
hw.mainKeys=yes

# === CAMERA ===
hw.camera.back=emulated
hw.camera.front=emulated

# === NETWORK ===
hw.wifi=yes

# === BOOT OPTIMIZATION ===
fastboot.forceFastBoot=yes
fastboot.forceColdBoot=no
```

### Configurazioni per Diversi Scenari

#### Gaming High-End
```ini
hw.ramSize=8192          # 8GB
hw.cpu.ncore=6           # 6 cores
hw.gpu.mode=host         # Hardware GPU
hw.lcd.width=1440
hw.lcd.height=2560
```

#### Gaming Bilanciato
```ini
hw.ramSize=4096          # 4GB
hw.cpu.ncore=4           # 4 cores
hw.gpu.mode=auto         # Auto GPU
hw.lcd.width=1080
hw.lcd.height=1920
```

#### Gaming Leggero
```ini
hw.ramSize=3072          # 3GB
hw.cpu.ncore=2           # 2 cores
hw.gpu.mode=swiftshader_indirect
hw.lcd.width=720
hw.lcd.height=1280
```

---

## 🎮 **OTTIMIZZAZIONI GAMING**

### Parametri Critici Gaming
1. **RAM**: Minimo 4GB, raccomandato 6-8GB
2. **CPU Cores**: 4+ cores per giochi moderni
3. **GPU Mode**: 
   - `host`: Massime prestazioni
   - `auto`: Bilanciato
   - `swiftshader_indirect`: Compatibilità

### Ottimizzazioni Sistema Host
```bash
# Linux: Governor performance
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Windows: High Performance mode
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c

# macOS: Prevent thermal throttling
sudo pmset -a thermalstate 0
```

### Emulator Launch Ottimizzato
```bash
# Comando avvio gaming
~/Android/Sdk/emulator/emulator \
    -avd "NOME_GIOCO" \
    -gpu host \
    -memory 6144 \
    -cores 4 \
    -accel auto \
    -no-snapshot-load \
    -wipe-data
```

---

## 🔧 **TROUBLESHOOTING**

### Errori Comuni

#### "CPU Architecture 'arm' is not supported"
```bash
# Causa: System image ARM su CPU Intel/AMD
# Soluzione: Usare x86_64 system image
-k "system-images;android-34;google_apis_playstore;x86_64"
```

#### "HAXM is not installed"
```bash
# Causa: HAXM deprecato
# Soluzione: Usare WHPX (Windows) o KVM (Linux)
# Windows: Abilitare Windows Hypervisor Platform
# Linux: Installare KVM
```

#### "Emulator: ERROR: x86 emulation requires hardware acceleration"
```bash
# Verifica accelerazione
emulator -accel-check

# Soluzioni:
# 1. Abilitare VT-x/AMD-V nel BIOS
# 2. Installare hypervisor corretto
# 3. Usare -gpu swiftshader_indirect
```

#### Prestazioni Lente
```bash
# Diagnosi
emulator -avd NOME -verbose

# Soluzioni:
# 1. Aumentare RAM: hw.ramSize=6144
# 2. Più CPU cores: hw.cpu.ncore=4
# 3. GPU hardware: hw.gpu.mode=host
# 4. Disabilitare animazioni Android
```

### Comandi Diagnostici
```bash
# Verifica system images
sdkmanager --list_installed | grep system-images

# Verifica AVD
emulator -list-avds

# Test accelerazione
emulator -accel-check

# Verifica GPU
emulator -avd NOME -gpu host -verbose
```

---

## 📊 **BEST PRACTICES 2025**

### DO ✅
- Usare sempre x86_64 su Intel/AMD
- Abilitare accelerazione hardware
- Configurare RAM adeguata (4-8GB)
- Usare SSD per AVD storage
- Aggiornare regolarmente SDK

### DON'T ❌
- Non usare ARM su Intel/AMD
- Non usare HAXM (deprecato)
- Non sottodimensionare RAM
- Non usare HDD per storage
- Non ignorare aggiornamenti

### Monitoraggio Prestazioni
```bash
# CPU usage
top -p $(pgrep qemu-system)

# Memory usage
ps aux | grep emulator

# GPU usage (Linux)
nvidia-smi

# Temperature monitoring
sensors
```

---

**📅 Ultimo aggiornamento**: Luglio 2025
**🔄 Versione documentazione**: 2025.1
**✅ Testato su**: Android Studio Narwhal 2025.1.1.14-1
