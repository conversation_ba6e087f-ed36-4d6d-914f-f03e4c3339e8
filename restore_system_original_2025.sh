#!/bin/bash

# ============================================================================
# RIPRISTINO SISTEMA ALLE CONDIZIONI ORIGINALI
# Annulla tutte le ottimizzazioni applicate per Android Studio/Emulatori
# ============================================================================

echo "🔄 RIPRISTINO SISTEMA ALLE CONDIZIONI ORIGINALI"
echo "==============================================="

# === FUNZIONI ===

restore_system_settings() {
    echo "⚙️  RIPRISTINO IMPOSTAZIONI SISTEMA"
    echo "=================================="
    
    echo "🔧 Ripristino vm.swappiness al valore di default..."
    echo 60 | sudo tee /proc/sys/vm/swappiness >/dev/null
    
    echo "🔧 Ripristino dirty ratios ai valori di default..."
    echo 20 | sudo tee /proc/sys/vm/dirty_ratio >/dev/null
    echo 10 | sudo tee /proc/sys/vm/dirty_background_ratio >/dev/null
    
    echo "🔧 Ripristino vfs_cache_pressure al valore di default..."
    echo 100 | sudo tee /proc/sys/vm/vfs_cache_pressure >/dev/null
    
    echo "✅ Impostazioni sistema ripristinate"
    echo ""
}

restore_cpu_governor() {
    echo "🖥️  RIPRISTINO CPU GOVERNOR"
    echo "=========================="
    
    echo "🔧 Ripristino CPU governor a 'ondemand' (default)..."
    for cpu in /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor; do
        if [ -f "$cpu" ]; then
            echo ondemand | sudo tee "$cpu" >/dev/null 2>&1
        fi
    done
    
    echo "✅ CPU governor ripristinato"
    echo ""
}

restore_io_scheduler() {
    echo "💿 RIPRISTINO I/O SCHEDULER"
    echo "=========================="
    
    echo "🔧 Ripristino I/O scheduler a 'mq-deadline' (default Arch)..."
    for disk in /sys/block/*/queue/scheduler; do
        if [ -f "$disk" ]; then
            # Verifica schedulers disponibili e usa il default
            if grep -q "mq-deadline" "$disk"; then
                echo mq-deadline | sudo tee "$disk" >/dev/null 2>&1
            elif grep -q "deadline" "$disk"; then
                echo deadline | sudo tee "$disk" >/dev/null 2>&1
            fi
        fi
    done
    
    echo "✅ I/O scheduler ripristinato"
    echo ""
}

remove_custom_configs() {
    echo "🗑️  RIMOZIONE CONFIGURAZIONI PERSONALIZZATE"
    echo "=========================================="
    
    echo "🔧 Rimozione configurazioni sysctl personalizzate..."
    if [ -f "/etc/sysctl.d/99-android-emulator.conf" ]; then
        sudo rm -f "/etc/sysctl.d/99-android-emulator.conf"
        echo "   ✅ Rimosso: /etc/sysctl.d/99-android-emulator.conf"
    fi
    
    echo "🔧 Rimozione configurazioni limiti personalizzate..."
    if [ -f "/etc/security/limits.d/99-android-emulator.conf" ]; then
        sudo rm -f "/etc/security/limits.d/99-android-emulator.conf"
        echo "   ✅ Rimosso: /etc/security/limits.d/99-android-emulator.conf"
    fi
    
    echo "✅ Configurazioni personalizzate rimosse"
    echo ""
}

restore_ulimits() {
    echo "📊 RIPRISTINO LIMITI UTENTE"
    echo "=========================="
    
    echo "🔧 Ripristino ulimits ai valori di default..."
    
    # Ripristina file descriptor limit (default: 1024)
    ulimit -n 1024
    
    # Ripristina process limit (default: varia, ma tipicamente 4096)
    ulimit -u 4096
    
    echo "✅ Limiti utente ripristinati"
    echo ""
}

remove_custom_scripts() {
    echo "🗑️  RIMOZIONE SCRIPT PERSONALIZZATI"
    echo "=================================="
    
    scripts_to_remove=(
        "$HOME/launch_android_studio_optimized.sh"
        "$HOME/monitor_emulator_resources.sh"
        "$HOME/.local/share/applications/android-studio-optimized.desktop"
    )
    
    for script in "${scripts_to_remove[@]}"; do
        if [ -f "$script" ]; then
            rm -f "$script"
            echo "   ✅ Rimosso: $script"
        fi
    done
    
    echo "✅ Script personalizzati rimossi"
    echo ""
}

restore_android_studio_config() {
    echo "📱 RIPRISTINO CONFIGURAZIONE ANDROID STUDIO"
    echo "=========================================="
    
    echo "🔧 Rimozione variabili ambiente personalizzate..."
    
    # Rimuovi eventuali export personalizzati da .bashrc
    if [ -f "$HOME/.bashrc" ]; then
        # Backup .bashrc
        cp "$HOME/.bashrc" "$HOME/.bashrc.backup.$(date +%Y%m%d_%H%M%S)"
        
        # Rimuovi righe aggiunte per Android Studio
        sed -i '/# Android Studio Optimizations/,/# End Android Studio Optimizations/d' "$HOME/.bashrc"
        sed -i '/STUDIO_JVM_OPTS/d' "$HOME/.bashrc"
        sed -i '/ANDROID_EMULATOR_USE_SYSTEM_LIBS/d' "$HOME/.bashrc"
    fi
    
    echo "✅ Configurazione Android Studio ripristinata"
    echo ""
}

restore_desktop_entry() {
    echo "🖥️  RIPRISTINO DESKTOP ENTRY ORIGINALE"
    echo "====================================="
    
    # Ripristina desktop entry originale Android Studio
    if [ -f "$HOME/.local/share/applications/android-studio.desktop" ]; then
        cat > "$HOME/.local/share/applications/android-studio.desktop" << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Android Studio
Icon=/opt/android-studio/bin/studio.png
Exec=/opt/android-studio/bin/studio
Comment=The Drive to Develop
Categories=Development;IDE;
Terminal=false
StartupWMClass=jetbrains-studio
StartupNotify=true
EOF
        echo "✅ Desktop entry originale ripristinato"
    fi
    
    echo ""
}

clean_temporary_optimizations() {
    echo "🧹 PULIZIA OTTIMIZZAZIONI TEMPORANEE"
    echo "==================================="
    
    echo "🔧 Rimozione file temporanei di ottimizzazione..."
    
    # Rimuovi eventuali file temporanei creati
    temp_files=(
        "/tmp/android-studio-optimization*"
        "/tmp/emulator-optimization*"
        "$HOME/.android-studio-optimized*"
    )
    
    for pattern in "${temp_files[@]}"; do
        for file in $pattern; do
            if [ -f "$file" ] || [ -d "$file" ]; then
                rm -rf "$file" 2>/dev/null
                echo "   ✅ Rimosso: $file"
            fi
        done
    done
    
    echo "✅ Pulizia completata"
    echo ""
}

verify_restoration() {
    echo "🔍 VERIFICA RIPRISTINO"
    echo "====================="
    
    echo "📊 Stato attuale sistema:"
    
    # Verifica swappiness
    current_swappiness=$(cat /proc/sys/vm/swappiness)
    echo "   vm.swappiness: $current_swappiness (dovrebbe essere 60)"
    
    # Verifica dirty ratio
    current_dirty=$(cat /proc/sys/vm/dirty_ratio)
    echo "   vm.dirty_ratio: $current_dirty (dovrebbe essere 20)"
    
    # Verifica CPU governor
    current_governor=$(cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor 2>/dev/null || echo "N/A")
    echo "   CPU governor: $current_governor (dovrebbe essere ondemand)"
    
    # Verifica ulimits
    current_nofile=$(ulimit -n)
    current_nproc=$(ulimit -u)
    echo "   File descriptor limit: $current_nofile (dovrebbe essere 1024)"
    echo "   Process limit: $current_nproc"
    
    # Verifica file di configurazione
    echo ""
    echo "📁 File di configurazione:"
    if [ -f "/etc/sysctl.d/99-android-emulator.conf" ]; then
        echo "   ❌ /etc/sysctl.d/99-android-emulator.conf ancora presente"
    else
        echo "   ✅ /etc/sysctl.d/99-android-emulator.conf rimosso"
    fi
    
    if [ -f "/etc/security/limits.d/99-android-emulator.conf" ]; then
        echo "   ❌ /etc/security/limits.d/99-android-emulator.conf ancora presente"
    else
        echo "   ✅ /etc/security/limits.d/99-android-emulator.conf rimosso"
    fi
    
    # Verifica script personalizzati
    echo ""
    echo "📜 Script personalizzati:"
    if [ -f "$HOME/launch_android_studio_optimized.sh" ]; then
        echo "   ❌ launch_android_studio_optimized.sh ancora presente"
    else
        echo "   ✅ launch_android_studio_optimized.sh rimosso"
    fi
    
    if [ -f "$HOME/monitor_emulator_resources.sh" ]; then
        echo "   ❌ monitor_emulator_resources.sh ancora presente"
    else
        echo "   ✅ monitor_emulator_resources.sh rimosso"
    fi
    
    echo ""
}

# === ESECUZIONE PRINCIPALE ===

echo "🔄 RIPRISTINO COMPLETO SISTEMA ALLE CONDIZIONI ORIGINALI"
echo "   • Annulla ottimizzazioni vm/cpu/io"
echo "   • Rimuove configurazioni personalizzate"
echo "   • Ripristina limiti di default"
echo "   • Rimuove script personalizzati"
echo ""

echo "⚠️  ATTENZIONE: Questa operazione annullerà tutte le ottimizzazioni"
echo "   applicate per Android Studio e gli emulatori."
echo ""
echo "❓ Procedere con il ripristino? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "🚀 INIZIO RIPRISTINO..."
    echo "====================="
    
    # Esegui ripristino
    restore_system_settings
    restore_cpu_governor
    restore_io_scheduler
    remove_custom_configs
    restore_ulimits
    remove_custom_scripts
    restore_android_studio_config
    restore_desktop_entry
    clean_temporary_optimizations
    
    # Verifica ripristino
    verify_restoration
    
    echo ""
    echo "🎉 RIPRISTINO COMPLETATO!"
    echo "========================"
    echo ""
    echo "✅ SISTEMA RIPRISTINATO ALLE CONDIZIONI ORIGINALI:"
    echo "   📊 vm.swappiness: 60 (default)"
    echo "   🖥️  CPU governor: ondemand (default)"
    echo "   💿 I/O scheduler: default"
    echo "   📁 Configurazioni personalizzate: rimosse"
    echo "   📜 Script personalizzati: rimossi"
    echo "   🔧 Limiti sistema: default"
    echo ""
    echo "💡 RACCOMANDAZIONI:"
    echo "   • Riavvia il sistema per applicare completamente le modifiche"
    echo "   • Android Studio tornerà al comportamento originale"
    echo "   • Gli emulatori useranno le configurazioni di default"
    echo ""
    echo "🚀 Per avviare Android Studio usa il comando originale:"
    echo "   /opt/android-studio/bin/studio"
    echo ""
    echo "✅ RIPRISTINO COMPLETATO CON SUCCESSO!"
    
else
    echo ""
    echo "❌ RIPRISTINO ANNULLATO"
    echo "Il sistema mantiene le ottimizzazioni attuali."
fi
