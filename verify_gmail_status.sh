#!/bin/bash

echo "🔍 VERIFICA STATO GMAIL NEGLI EMULATORI"
echo "======================================="

ADB_PATH="$HOME/Android/Sdk/platform-tools/adb"

# Trova emulatori attivi
DEVICES=$($ADB_PATH devices | grep emulator | grep -E "(device|online)" | cut -f1)

if [ -z "$DEVICES" ]; then
    echo "❌ Nessun emulatore attivo"
    exit 1
fi

DEVICE_COUNT=$(echo "$DEVICES" | wc -l)
echo "📱 Verificando $DEVICE_COUNT emulatori attivi..."
echo ""

for device_id in $DEVICES; do
    echo "🎮 Emulatore: $device_id"
    echo "   ----------------------------------------"
    
    # Verifica se Gmail è installato
    if $ADB_PATH -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
        echo "   📦 Pacchetto Gmail: PRESENTE"
        
        # Verifica se è abilitato
        if $ADB_PATH -s "$device_id" shell pm list packages -d | grep -q "com.google.android.gm"; then
            echo "   🔴 Stato Gmail: DISABILITATO ✅"
        else
            echo "   🟢 Stato Gmail: ABILITATO ❌"
        fi
        
        # Verifica se è visibile nel launcher
        if $ADB_PATH -s "$device_id" shell cmd package query-activities --brief com.google.android.gm 2>/dev/null | grep -q "com.google.android.gm"; then
            echo "   👁️  Visibilità: VISIBILE NEL LAUNCHER ❌"
        else
            echo "   👁️  Visibilità: NASCOSTO DAL LAUNCHER ✅"
        fi
        
    else
        echo "   📦 Pacchetto Gmail: COMPLETAMENTE RIMOSSO ✅"
    fi
    
    # Verifica Play Store
    if $ADB_PATH -s "$device_id" shell pm list packages | grep -q "com.android.vending"; then
        if $ADB_PATH -s "$device_id" shell pm list packages -d | grep -q "com.android.vending"; then
            echo "   🏪 Play Store: DISABILITATO ❌"
        else
            echo "   🏪 Play Store: ATTIVO ✅"
        fi
    else
        echo "   🏪 Play Store: NON TROVATO ❌"
    fi
    
    echo ""
done

echo "📋 RIASSUNTO:"
echo "============="

gmail_disabled=0
gmail_hidden=0
playstore_active=0

for device_id in $DEVICES; do
    # Conta Gmail disabilitati
    if $ADB_PATH -s "$device_id" shell pm list packages -d | grep -q "com.google.android.gm"; then
        ((gmail_disabled++))
    fi
    
    # Conta Gmail nascosti
    if ! $ADB_PATH -s "$device_id" shell cmd package query-activities --brief com.google.android.gm 2>/dev/null | grep -q "com.google.android.gm"; then
        ((gmail_hidden++))
    fi
    
    # Conta Play Store attivi
    if $ADB_PATH -s "$device_id" shell pm list packages | grep -q "com.android.vending" && ! $ADB_PATH -s "$device_id" shell pm list packages -d | grep -q "com.android.vending"; then
        ((playstore_active++))
    fi
done

echo "📧 Gmail disabilitato: $gmail_disabled/$DEVICE_COUNT"
echo "👁️  Gmail nascosto: $gmail_hidden/$DEVICE_COUNT"
echo "🏪 Play Store attivo: $playstore_active/$DEVICE_COUNT"

if [ $gmail_disabled -eq $DEVICE_COUNT ] && [ $playstore_active -eq $DEVICE_COUNT ]; then
    echo ""
    echo "🎉 OBIETTIVO RAGGIUNTO!"
    echo "   ✅ Gmail disabilitato su tutti gli emulatori"
    echo "   ✅ Play Store attivo su tutti gli emulatori"
    echo "   🎮 Emulatori pronti per il gaming!"
else
    echo ""
    echo "⚠️  Alcuni emulatori necessitano ancora interventi"
fi
