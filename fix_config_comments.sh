#!/bin/bash

echo "🔧 CORREZIONE COMMENTI CONFIG.INI"
echo "=================================="

AVD_DIR="$HOME/.config/.android/avd"

# Lista tutti gli emulatori
games=(
    "ASTRA_Knights_of_Veda"
    "Aether_Gazer"
    "Ash_Echoes"
    "Blood_Strike"
    "Brown_Dust_2"
    "Genshin_Impact"
    "Honkai_Star_Rail"
    "Zenless_Zone_Zero"
    "Infinity_Nikki"
    "NIKKE_Goddess_of_Victory"
    "Epic_Seven"
    "Arknights"
    "Punishing_Gray_Raven"
    "Reverse_1999"
    "Solo_Leveling_Arise"
    "Snowbreak_Containment_Zone"
    "Dislyte"
    "CookieRun_Kingdom"
    "Path_to_Nowhere"
    "CounterSide"
    "Eversoul"
    "Ace_Racer"
    "Jujutsu_Kaisen_Phantom_Parade"
    "Higan_Eruthyll"
    "MementoMori_AFKRPG"
    "Figure_Fantasy"
    "Tower_of_God_NEW_WORLD"
    "Echocalypse_Scarlet_Covenant"
    "OUTERPLANE_Strategy_Anime"
    "Uma_Musume_Pretty_Derby"
    "CookieRun_OvenBreak"
    "CookieRun_Tower_of_Adventures"
    "Cat_Fantasy_Isekai_Adventure"
    "Go_Go_Muffin"
    "DanMachi_BATTLE_CHRONICLE"
    "Farlight_84"
    "Girls_Frontline_2_Exilium"
    "Heaven_Burns_Red"
    "Etheria_Restart"
    "Black_Beacon"
    "Metal_Slug_Awakening"
    "Ni_no_Kuni_Cross_Worlds"
    "Phantom_Blade_Executioners"
    "STARSEED_Asnia_Trigger"
    "Neural_Cloud"
    "Tower_of_God_Great_Journey"
    "Wuthering_Waves"
)

success_count=0
total_games=${#games[@]}

for game_name in "${games[@]}"; do
    config_file="$AVD_DIR/${game_name}.avd/config.ini"
    
    if [ -f "$config_file" ]; then
        echo "🔧 Correggendo: $game_name"
        
        # Rimuovi tutti i commenti dalle righe di configurazione
        sed -i 's/\s*#.*$//' "$config_file"
        
        # Rimuovi righe vuote multiple
        sed -i '/^$/N;/^\n$/d' "$config_file"
        
        ((success_count++))
    else
        echo "❌ Config non trovato: $game_name"
    fi
done

echo ""
echo "📊 RISULTATI:"
echo "✅ Corretti: $success_count/$total_games"
echo ""
echo "🧪 Test configurazione Genshin_Impact:"
if [ -f "$AVD_DIR/Genshin_Impact.avd/config.ini" ]; then
    echo "Architettura corretta:"
    grep "abi.type" "$AVD_DIR/Genshin_Impact.avd/config.ini"
fi
