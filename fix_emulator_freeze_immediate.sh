#!/bin/bash

# ============================================================================
# FIX IMMEDIATO BLOCCO EMULATORI - LIBERAZIONE SPAZIO + OTTIMIZZAZIONI
# Risolve il blocco del sistema con emulatori multipli
# ============================================================================

echo "🚨 FIX IMMEDIATO BLOCCO EMULATORI"
echo "================================="

# === FUNZIONI ===

check_disk_space() {
    echo "💿 VERIFICA SPAZIO DISCO"
    echo "======================="
    
    df -h /
    
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    AVAILABLE_GB=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    
    echo ""
    echo "📊 Stato attuale:"
    echo "   Utilizzo: ${DISK_USAGE}%"
    echo "   Disponibile: ${AVAILABLE_GB}GB"
    
    if [ $DISK_USAGE -gt 80 ]; then
        echo "   🚨 CRITICO: Spazio insufficiente per emulatori!"
    elif [ $DISK_USAGE -gt 70 ]; then
        echo "   ⚠️  ATTENZIONE: Spazio limitato"
    else
        echo "   ✅ Spazio sufficiente"
    fi
    
    echo ""
}

free_disk_space() {
    echo "🧹 LIBERAZIONE SPAZIO DISCO"
    echo "=========================="
    
    SPACE_BEFORE=$(df / | awk 'NR==2 {print $4}')
    
    echo "🗑️  Pulizia cache Android Studio..."
    rm -rf ~/.cache/Google/AndroidStudio*/tmp/* 2>/dev/null
    rm -rf ~/.cache/Google/AndroidStudio*/system/caches/* 2>/dev/null
    rm -rf ~/.cache/Google/AndroidStudio*/system/index/* 2>/dev/null
    echo "   ✅ Cache Android Studio pulita"
    
    echo "🗑️  Pulizia snapshots emulatori..."
    rm -rf ~/.config/.android/avd/*/snapshots/default_boot 2>/dev/null
    rm -rf ~/.config/.android/avd/*/snapshots/snap_* 2>/dev/null
    echo "   ✅ Snapshots emulatori rimossi"
    
    echo "🗑️  Pulizia log files..."
    rm -rf ~/.config/Google/AndroidStudio*/log/* 2>/dev/null
    rm -rf ~/.android/avd/*.log 2>/dev/null
    rm -rf ~/.android/*.log 2>/dev/null
    echo "   ✅ Log files puliti"
    
    echo "🗑️  Pulizia build cache..."
    rm -rf ~/Android/Sdk/build-cache/* 2>/dev/null
    rm -rf ~/.gradle/caches/* 2>/dev/null
    echo "   ✅ Build cache pulita"
    
    echo "🗑️  Pulizia cache sistema..."
    sudo pacman -Scc --noconfirm 2>/dev/null || echo "   ⚠️  Pacman cache non pulita (non Arch?)"
    
    echo "🗑️  Pulizia file temporanei..."
    rm -rf /tmp/android* 2>/dev/null
    rm -rf /tmp/emulator* 2>/dev/null
    rm -rf /tmp/studio* 2>/dev/null
    echo "   ✅ File temporanei puliti"
    
    SPACE_AFTER=$(df / | awk 'NR==2 {print $4}')
    FREED_KB=$((SPACE_AFTER - SPACE_BEFORE))
    FREED_GB=$((FREED_KB / 1024 / 1024))
    
    echo ""
    echo "✅ SPAZIO LIBERATO: ${FREED_GB}GB"
    echo ""
}

optimize_emulator_configs() {
    echo "⚙️  OTTIMIZZAZIONE CONFIGURAZIONI EMULATORI"
    echo "=========================================="
    
    EMULATOR_COUNT=0
    
    for avd_dir in ~/.config/.android/avd/*.avd; do
        if [ -d "$avd_dir" ]; then
            config_file="$avd_dir/config.ini"
            if [ -f "$config_file" ]; then
                EMULATOR_COUNT=$((EMULATOR_COUNT + 1))
                emulator_name=$(basename "$avd_dir" .avd)
                
                echo "🔧 Ottimizzando: $emulator_name"
                
                # Backup configurazione originale
                cp "$config_file" "$config_file.backup.$(date +%Y%m%d_%H%M%S)"
                
                # Ottimizzazioni anti-blocco
                sed -i 's/hw.ramSize=.*/hw.ramSize=2048/' "$config_file"  # 2GB RAM
                sed -i 's/hw.cpu.ncore=.*/hw.cpu.ncore=2/' "$config_file"  # 2 CPU cores
                sed -i 's/disk.dataPartition.size=.*/disk.dataPartition.size=2048m/' "$config_file"  # 2GB storage
                
                # Aggiungi ottimizzazioni se non presenti
                if ! grep -q "hw.gpu.enabled" "$config_file"; then
                    echo "hw.gpu.enabled=yes" >> "$config_file"
                fi
                
                if ! grep -q "hw.gpu.mode" "$config_file"; then
                    echo "hw.gpu.mode=host" >> "$config_file"
                fi
                
                # Disabilita snapshots per risparmiare spazio
                sed -i 's/snapshot.present=.*/snapshot.present=no/' "$config_file"
                
                echo "   ✅ $emulator_name ottimizzato (2GB RAM, 2 cores)"
            fi
        fi
    done
    
    echo ""
    echo "✅ $EMULATOR_COUNT emulatori ottimizzati"
    echo ""
}

apply_system_optimizations() {
    echo "⚡ OTTIMIZZAZIONI SISTEMA ANTI-BLOCCO"
    echo "===================================="
    
    echo "🔧 Riduzione swap thrashing..."
    echo 10 | sudo tee /proc/sys/vm/swappiness >/dev/null
    echo "   vm.swappiness = 10 (era 60)"
    
    echo "🔧 Ottimizzazione I/O per evitare blocchi..."
    echo 5 | sudo tee /proc/sys/vm/dirty_ratio >/dev/null
    echo 2 | sudo tee /proc/sys/vm/dirty_background_ratio >/dev/null
    echo "   dirty_ratio = 5% (era 20%)"
    echo "   dirty_background_ratio = 2% (era 10%)"
    
    echo "🔧 Riduzione cache pressure..."
    echo 200 | sudo tee /proc/sys/vm/vfs_cache_pressure >/dev/null
    echo "   vfs_cache_pressure = 200 (era 100)"
    
    echo "✅ Ottimizzazioni sistema applicate"
    echo ""
}

create_watchdog_script() {
    echo "🛡️  CREAZIONE SCRIPT WATCHDOG ANTI-BLOCCO"
    echo "========================================"
    
    cat > "$HOME/emulator_watchdog.sh" << 'EOF'
#!/bin/bash

# ============================================================================
# WATCHDOG ANTI-BLOCCO EMULATORI
# Monitora sistema e termina emulatori se risorse critiche
# ============================================================================

echo "🛡️  WATCHDOG ANTI-BLOCCO ATTIVO"
echo "Monitoraggio ogni 10 secondi..."
echo "Premi Ctrl+C per fermare"
echo ""

while true; do
    # Monitora RAM
    RAM_USAGE=$(free | awk '/^Mem:/ {printf "%.0f", $3/$2 * 100}')
    
    # Monitora spazio disco
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    # Monitora I/O wait
    IO_WAIT=$(iostat -c 1 1 2>/dev/null | awk '/avg-cpu/ {getline; print $4}' | cut -d. -f1 2>/dev/null || echo "0")
    
    # Conta emulatori attivi
    EMU_COUNT=$(pgrep -f "qemu-system" | wc -l)
    
    # Status normale
    echo "$(date '+%H:%M:%S') - RAM: ${RAM_USAGE}% | Disk: ${DISK_USAGE}% | I/O: ${IO_WAIT}% | Emulatori: $EMU_COUNT"
    
    # Controlli critici
    CRITICAL=false
    
    if [ $RAM_USAGE -gt 90 ]; then
        echo "🚨 RAM CRITICA (${RAM_USAGE}%) - Terminando emulatori"
        CRITICAL=true
    fi
    
    if [ $DISK_USAGE -gt 85 ]; then
        echo "🚨 DISCO CRITICO (${DISK_USAGE}%) - Terminando emulatori"
        CRITICAL=true
    fi
    
    if [ $IO_WAIT -gt 30 ]; then
        echo "🚨 I/O WAIT CRITICO (${IO_WAIT}%) - Terminando emulatori"
        CRITICAL=true
    fi
    
    if [ $EMU_COUNT -gt 2 ]; then
        echo "⚠️  TROPPI EMULATORI ($EMU_COUNT > 2) - Terminando il più vecchio"
        pkill -o -f "qemu-system"
    fi
    
    if [ "$CRITICAL" = true ]; then
        echo "🚨 EMERGENZA: Terminazione forzata tutti gli emulatori"
        pkill -9 -f "qemu-system"
        pkill -9 -f "emulator"
        
        # Libera memoria
        echo 3 | sudo tee /proc/sys/vm/drop_caches >/dev/null 2>&1
        
        echo "✅ Sistema liberato - Attendi 30 secondi prima di riavviare emulatori"
        sleep 30
    fi
    
    sleep 10
done
EOF
    
    chmod +x "$HOME/emulator_watchdog.sh"
    
    echo "✅ Watchdog creato: $HOME/emulator_watchdog.sh"
    echo "   💡 Uso: bash ~/emulator_watchdog.sh"
    echo ""
}

create_monitoring_script() {
    echo "📊 CREAZIONE SCRIPT MONITORAGGIO"
    echo "==============================="
    
    cat > "$HOME/monitor_emulator_health.sh" << 'EOF'
#!/bin/bash

echo "📊 MONITORAGGIO SALUTE SISTEMA EMULATORI"
echo "========================================"
echo "Data: $(date)"
echo ""

# RAM
echo "💾 MEMORIA:"
free -h
RAM_USAGE=$(free | awk '/^Mem:/ {printf "%.1f", $3/$2 * 100}')
echo "Utilizzo RAM: ${RAM_USAGE}% $([ ${RAM_USAGE%.*} -gt 85 ] && echo '🚨 CRITICO' || echo '✅ OK')"
echo ""

# Disk
echo "💿 SPAZIO DISCO:"
df -h /
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
echo "Utilizzo Disco: ${DISK_USAGE}% $([ $DISK_USAGE -gt 80 ] && echo '🚨 CRITICO' || echo '✅ OK')"
echo ""

# Emulatori
echo "📱 EMULATORI ATTIVI:"
EMU_COUNT=$(pgrep -f "qemu-system" | wc -l)
echo "Numero emulatori: $EMU_COUNT $([ $EMU_COUNT -gt 2 ] && echo '⚠️ TROPPI' || echo '✅ OK')"

if [ $EMU_COUNT -gt 0 ]; then
    echo "Processi emulatori:"
    ps aux | grep qemu-system | grep -v grep | awk '{print "  PID: " $2 " | RAM: " $6/1024 "MB | CPU: " $3 "%"}'
fi
echo ""

# CPU
echo "🖥️  CPU:"
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1 "%"}')
echo "Utilizzo CPU: $CPU_USAGE"

# Temperatura (se disponibile)
TEMP=$(sensors 2>/dev/null | grep -i core | head -1 | grep -o '[0-9]*°C' | head -1 || echo "N/A")
echo "Temperatura CPU: $TEMP"
echo ""

# I/O
echo "⏱️  I/O WAIT:"
IO_WAIT=$(iostat -c 1 1 2>/dev/null | awk '/avg-cpu/ {getline; print $4}' | cut -d. -f1 2>/dev/null || echo "N/A")
echo "I/O Wait: ${IO_WAIT}% $([ "$IO_WAIT" != "N/A" ] && [ $IO_WAIT -gt 20 ] && echo '⚠️ ALTO' || echo '✅ OK')"
echo ""

# Raccomandazioni
echo "💡 RACCOMANDAZIONI:"
if [ ${RAM_USAGE%.*} -gt 85 ] || [ $DISK_USAGE -gt 80 ] || [ $EMU_COUNT -gt 2 ]; then
    echo "🚨 SISTEMA A RISCHIO BLOCCO!"
    echo "   • Chiudi alcuni emulatori"
    echo "   • Libera spazio disco se >80%"
    echo "   • Usa watchdog: bash ~/emulator_watchdog.sh"
else
    echo "✅ Sistema stabile per emulatori"
fi

echo ""
echo "🛡️  Per protezione automatica: bash ~/emulator_watchdog.sh"
EOF
    
    chmod +x "$HOME/monitor_emulator_health.sh"
    
    echo "✅ Monitoraggio creato: $HOME/monitor_emulator_health.sh"
    echo "   💡 Uso: bash ~/monitor_emulator_health.sh"
    echo ""
}

# === ESECUZIONE PRINCIPALE ===

echo "🚨 FIX IMMEDIATO BLOCCO EMULATORI CON 3+ ISTANZE"
echo "   • Libera spazio disco critico"
echo "   • Riduce RAM emulatori (6GB → 2GB)"
echo "   • Ottimizza sistema anti-blocco"
echo "   • Crea watchdog protezione"
echo ""

# Step 1: Verifica spazio
check_disk_space

# Step 2: Libera spazio
free_disk_space

# Step 3: Ottimizza emulatori
optimize_emulator_configs

# Step 4: Ottimizza sistema
apply_system_optimizations

# Step 5: Crea watchdog
create_watchdog_script

# Step 6: Crea monitoraggio
create_monitoring_script

# Verifica finale
echo "🔍 VERIFICA FINALE"
echo "=================="
check_disk_space

echo "🎉 FIX COMPLETATO!"
echo "================="
echo ""
echo "✅ PROBLEMI RISOLTI:"
echo "   🗑️  Spazio disco liberato"
echo "   ⚙️  Emulatori ottimizzati (2GB RAM, 2 cores)"
echo "   ⚡ Sistema ottimizzato anti-blocco"
echo "   🛡️  Watchdog anti-blocco creato"
echo ""
echo "🎯 UTILIZZO SICURO:"
echo "   • Massimo 2 emulatori simultanei"
echo "   • Monitora: bash ~/monitor_emulator_health.sh"
echo "   • Protezione: bash ~/emulator_watchdog.sh"
echo ""
echo "⚠️  IMPORTANTE:"
echo "   • Riavvia Android Studio per applicare le modifiche"
echo "   • Tieni sempre >15GB spazio libero"
echo "   • Usa il watchdog quando usi emulatori multipli"
echo ""
echo "✅ SISTEMA PRONTO - NESSUN BLOCCO CON EMULATORI!"
